<template>
  <div class="cliente-filters">
    <div class="card">
      <div class="card-body">
        <div class="row">
          <!-- Búsqueda -->
          <div class="col-md-4 mb-3">
            <label class="form-label">Buscar Cliente</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-search"></i>
              </span>
              <input
                v-model="localFilters.search"
                type="text"
                class="form-control"
                placeholder="Nombre, DNI, email, teléfono..."
                @input="debouncedSearch"
              >
              <button 
                v-if="localFilters.search"
                @click="clearSearch"
                class="btn btn-outline-secondary"
                type="button"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Estado -->
          <div class="col-md-2 mb-3">
            <label class="form-label">Estado</label>
            <select v-model="localFilters.estado" class="form-select" @change="applyFilters">
              <option value="">Todos</option>
              <option value="posible">Posible</option>
              <option value="confirmado">Confirmado</option>
              <option value="activo">Activo</option>
              <option value="inactivo">Inactivo</option>
            </select>
          </div>

          <!-- Ordenar por -->
          <div class="col-md-2 mb-3">
            <label class="form-label">Ordenar por</label>
            <select v-model="localFilters.sortBy" class="form-select" @change="applyFilters">
              <option value="nombre">Nombre</option>
              <option value="apellidos">Apellidos</option>
              <option value="email">Email</option>
              <option value="created_at">Fecha registro</option>
              <option value="updated_at">Última actualización</option>
            </select>
          </div>

          <!-- Orden -->
          <div class="col-md-2 mb-3">
            <label class="form-label">Orden</label>
            <select v-model="localFilters.sortOrder" class="form-select" @change="applyFilters">
              <option value="asc">Ascendente</option>
              <option value="desc">Descendente</option>
            </select>
          </div>

          <!-- Acciones -->
          <div class="col-md-2 mb-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-1">
              <button
                @click="clearAllFilters"
                class="btn btn-outline-secondary flex-fill"
                title="Limpiar filtros"
              >
                <i class="fas fa-times"></i>
              </button>
              <button
                @click="toggleAdvanced"
                class="btn btn-outline-primary flex-fill"
                :class="{ active: showAdvanced }"
                title="Filtros avanzados"
              >
                <i class="fas fa-filter"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Filtros avanzados -->
        <div v-if="showAdvanced" class="row border-top pt-3">
          <div class="col-12 mb-3">
            <h6 class="text-primary">
              <i class="fas fa-sliders-h me-2"></i>
              Filtros Avanzados
            </h6>
          </div>

          <!-- Fecha de registro -->
          <div class="col-md-3 mb-3">
            <label class="form-label">Registrado desde</label>
            <input
              v-model="localFilters.fechaDesde"
              type="date"
              class="form-control"
              @change="applyFilters"
            >
          </div>

          <div class="col-md-3 mb-3">
            <label class="form-label">Registrado hasta</label>
            <input
              v-model="localFilters.fechaHasta"
              type="date"
              class="form-control"
              @change="applyFilters"
            >
          </div>

          <!-- Con alquileres -->
          <div class="col-md-3 mb-3">
            <label class="form-label">Alquileres</label>
            <select v-model="localFilters.conAlquileres" class="form-select" @change="applyFilters">
              <option value="">Todos</option>
              <option value="con">Con alquileres</option>
              <option value="sin">Sin alquileres</option>
            </select>
          </div>

          <!-- Acciones rápidas -->
          <div class="col-md-3 mb-3">
            <label class="form-label">Acciones rápidas</label>
            <div class="d-flex gap-1">
              <button
                @click="setQuickFilter('nuevos')"
                class="btn btn-sm btn-outline-info flex-fill"
                title="Clientes nuevos (últimos 7 días)"
              >
                Nuevos
              </button>
              <button
                @click="setQuickFilter('activos')"
                class="btn btn-sm btn-outline-success flex-fill"
                title="Solo clientes activos"
              >
                Activos
              </button>
            </div>
          </div>
        </div>

        <!-- Resumen de filtros activos -->
        <div v-if="hasActiveFilters" class="row border-top pt-3">
          <div class="col-12">
            <div class="d-flex flex-wrap gap-2 align-items-center">
              <span class="text-muted small">Filtros activos:</span>
              
              <span v-if="localFilters.search" class="badge bg-primary">
                Búsqueda: "{{ localFilters.search }}"
                <button @click="clearSearch" class="btn-close btn-close-white ms-1" style="font-size: 0.6em;"></button>
              </span>
              
              <span v-if="localFilters.estado" class="badge bg-info">
                Estado: {{ getEstadoText(localFilters.estado) }}
                <button @click="clearEstado" class="btn-close btn-close-white ms-1" style="font-size: 0.6em;"></button>
              </span>
              
              <span v-if="localFilters.fechaDesde || localFilters.fechaHasta" class="badge bg-warning text-dark">
                Fecha: {{ formatDateRange() }}
                <button @click="clearDateRange" class="btn-close ms-1" style="font-size: 0.6em;"></button>
              </span>
              
              <span v-if="localFilters.conAlquileres" class="badge bg-secondary">
                {{ localFilters.conAlquileres === 'con' ? 'Con alquileres' : 'Sin alquileres' }}
                <button @click="clearAlquileres" class="btn-close btn-close-white ms-1" style="font-size: 0.6em;"></button>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { debounce } from 'lodash-es'

export default {
  name: 'ClienteFilters',
  props: {
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:filters', 'clear'],
  setup(props, { emit }) {
    const showAdvanced = ref(false)
    
    const localFilters = ref({
      search: '',
      estado: '',
      sortBy: 'nombre',
      sortOrder: 'asc',
      fechaDesde: '',
      fechaHasta: '',
      conAlquileres: '',
      ...props.filters
    })

    const hasActiveFilters = computed(() => {
      return localFilters.value.search ||
             localFilters.value.estado ||
             localFilters.value.fechaDesde ||
             localFilters.value.fechaHasta ||
             localFilters.value.conAlquileres ||
             localFilters.value.sortBy !== 'nombre' ||
             localFilters.value.sortOrder !== 'asc'
    })

    // Debounced search
    const debouncedSearch = debounce(() => {
      applyFilters()
    }, 300)

    const applyFilters = () => {
      emit('update:filters', { ...localFilters.value })
    }

    const clearSearch = () => {
      localFilters.value.search = ''
      applyFilters()
    }

    const clearEstado = () => {
      localFilters.value.estado = ''
      applyFilters()
    }

    const clearDateRange = () => {
      localFilters.value.fechaDesde = ''
      localFilters.value.fechaHasta = ''
      applyFilters()
    }

    const clearAlquileres = () => {
      localFilters.value.conAlquileres = ''
      applyFilters()
    }

    const clearAllFilters = () => {
      localFilters.value = {
        search: '',
        estado: '',
        sortBy: 'nombre',
        sortOrder: 'asc',
        fechaDesde: '',
        fechaHasta: '',
        conAlquileres: ''
      }
      showAdvanced.value = false
      emit('clear')
      applyFilters()
    }

    const toggleAdvanced = () => {
      showAdvanced.value = !showAdvanced.value
    }

    const setQuickFilter = (type) => {
      if (type === 'nuevos') {
        const fecha = new Date()
        fecha.setDate(fecha.getDate() - 7)
        localFilters.value.fechaDesde = fecha.toISOString().split('T')[0]
        localFilters.value.sortBy = 'created_at'
        localFilters.value.sortOrder = 'desc'
      } else if (type === 'activos') {
        localFilters.value.estado = 'activo'
      }
      applyFilters()
    }

    const getEstadoText = (estado) => {
      const texts = {
        'posible': 'Posible',
        'confirmado': 'Confirmado',
        'activo': 'Activo',
        'inactivo': 'Inactivo'
      }
      return texts[estado] || estado
    }

    const formatDateRange = () => {
      const desde = localFilters.value.fechaDesde
      const hasta = localFilters.value.fechaHasta
      
      if (desde && hasta) {
        return `${formatDate(desde)} - ${formatDate(hasta)}`
      } else if (desde) {
        return `Desde ${formatDate(desde)}`
      } else if (hasta) {
        return `Hasta ${formatDate(hasta)}`
      }
      return ''
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('es-ES')
    }

    // Watch for external filter changes
    watch(() => props.filters, (newFilters) => {
      localFilters.value = { ...localFilters.value, ...newFilters }
    }, { deep: true })

    return {
      showAdvanced,
      localFilters,
      hasActiveFilters,
      debouncedSearch,
      applyFilters,
      clearSearch,
      clearEstado,
      clearDateRange,
      clearAlquileres,
      clearAllFilters,
      toggleAdvanced,
      setQuickFilter,
      getEstadoText,
      formatDateRange
    }
  }
}
</script>

<style scoped>
.btn.active {
  background-color: #4e73df;
  border-color: #4e73df;
  color: white;
}

.badge .btn-close {
  padding: 0;
  margin: 0;
}

.text-primary {
  color: #4e73df !important;
}

@media (max-width: 768px) {
  .d-flex.gap-1 {
    gap: 0.25rem !important;
  }
  
  .flex-fill {
    min-width: 0;
  }
}
</style>
