<template>
  <div class="modal fade" :id="modalId" tabindex="-1" ref="modalRef">
    <div class="modal-dialog" :class="modalSizeClass">
      <div class="modal-content">
        <div class="modal-header" :class="headerClass">
          <h5 class="modal-title">
            <i v-if="icon" :class="icon" class="me-2"></i>
            {{ title }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
        </div>
        
        <div class="modal-body">
          <!-- Contenido personalizado -->
          <slot>
            <p>{{ message }}</p>
          </slot>
          
          <!-- Información adicional -->
          <div v-if="details" class="alert" :class="alertClass">
            <i :class="alertIcon" class="me-2"></i>
            {{ details }}
          </div>
          
          <!-- Lista de elementos afectados -->
          <div v-if="affectedItems && affectedItems.length > 0" class="mt-3">
            <h6 class="text-muted">Elementos afectados:</h6>
            <ul class="list-group list-group-flush">
              <li v-for="item in affectedItems" :key="item.id" class="list-group-item px-0">
                <i :class="item.icon || 'fas fa-circle'" class="text-muted me-2"></i>
                {{ item.name || item.title }}
                <span v-if="item.description" class="text-muted small d-block">
                  {{ item.description }}
                </span>
              </li>
            </ul>
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            data-bs-dismiss="modal"
            :disabled="isLoading"
          >
            {{ cancelText }}
          </button>
          <button 
            type="button" 
            class="btn" 
            :class="confirmButtonClass"
            @click="handleConfirm"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status">
              <span class="visually-hidden">Cargando...</span>
            </span>
            <i v-else-if="confirmIcon" :class="confirmIcon" class="me-1"></i>
            {{ isLoading ? loadingText : confirmText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Modal } from 'bootstrap'

export default {
  name: 'ConfirmModal',
  props: {
    modalId: {
      type: String,
      default: 'confirmModal'
    },
    title: {
      type: String,
      default: 'Confirmar acción'
    },
    message: {
      type: String,
      default: '¿Estás seguro de que deseas continuar?'
    },
    details: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: 'warning', // success, info, warning, danger
      validator: (value) => ['success', 'info', 'warning', 'danger'].includes(value)
    },
    size: {
      type: String,
      default: 'md', // sm, md, lg, xl
      validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    confirmText: {
      type: String,
      default: 'Confirmar'
    },
    cancelText: {
      type: String,
      default: 'Cancelar'
    },
    loadingText: {
      type: String,
      default: 'Procesando...'
    },
    confirmIcon: {
      type: String,
      default: null
    },
    affectedItems: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'cancel', 'show', 'hide'],
  setup(props, { emit }) {
    const modalRef = ref(null)
    let modalInstance = null

    const modalSizeClass = computed(() => {
      const sizeClasses = {
        'sm': 'modal-sm',
        'md': '',
        'lg': 'modal-lg',
        'xl': 'modal-xl'
      }
      return sizeClasses[props.size] || ''
    })

    const headerClass = computed(() => {
      const classes = {
        'success': 'bg-success text-white',
        'info': 'bg-info text-white',
        'warning': 'bg-warning text-dark',
        'danger': 'bg-danger text-white'
      }
      return classes[props.type] || 'bg-warning text-dark'
    })

    const alertClass = computed(() => {
      const classes = {
        'success': 'alert-success',
        'info': 'alert-info',
        'warning': 'alert-warning',
        'danger': 'alert-danger'
      }
      return classes[props.type] || 'alert-warning'
    })

    const confirmButtonClass = computed(() => {
      const classes = {
        'success': 'btn-success',
        'info': 'btn-info',
        'warning': 'btn-warning',
        'danger': 'btn-danger'
      }
      return classes[props.type] || 'btn-warning'
    })

    const icon = computed(() => {
      const icons = {
        'success': 'fas fa-check-circle',
        'info': 'fas fa-info-circle',
        'warning': 'fas fa-exclamation-triangle',
        'danger': 'fas fa-exclamation-circle'
      }
      return icons[props.type] || 'fas fa-exclamation-triangle'
    })

    const alertIcon = computed(() => {
      const icons = {
        'success': 'fas fa-check',
        'info': 'fas fa-info',
        'warning': 'fas fa-exclamation-triangle',
        'danger': 'fas fa-exclamation-circle'
      }
      return icons[props.type] || 'fas fa-exclamation-triangle'
    })

    const handleConfirm = () => {
      emit('confirm')
    }

    const show = () => {
      if (modalInstance) {
        modalInstance.show()
      }
    }

    const hide = () => {
      if (modalInstance) {
        modalInstance.hide()
      }
    }

    onMounted(() => {
      if (modalRef.value) {
        modalInstance = new Modal(modalRef.value)
        
        modalRef.value.addEventListener('show.bs.modal', () => {
          emit('show')
        })
        
        modalRef.value.addEventListener('hide.bs.modal', () => {
          emit('hide')
        })
        
        modalRef.value.addEventListener('hidden.bs.modal', () => {
          emit('cancel')
        })
      }
    })

    onUnmounted(() => {
      if (modalInstance) {
        modalInstance.dispose()
      }
    })

    return {
      modalRef,
      modalSizeClass,
      headerClass,
      alertClass,
      confirmButtonClass,
      icon,
      alertIcon,
      handleConfirm,
      show,
      hide
    }
  }
}
</script>

<style scoped>
.modal-header.bg-success,
.modal-header.bg-info,
.modal-header.bg-danger {
  border-bottom: none;
}

.modal-header.bg-warning {
  border-bottom: 1px solid #ffeaa7;
}

.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.list-group-item {
  border: none;
  padding: 0.5rem 0;
}

.list-group-item:not(:last-child) {
  border-bottom: 1px solid #e3e6f0;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

@media (max-width: 576px) {
  .modal-dialog {
    margin: 1rem;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .modal-footer .btn {
    width: 100%;
  }
}
</style>
