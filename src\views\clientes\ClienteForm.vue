<template>
  <div class="cliente-form">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">
                <i class="fas fa-user me-2"></i>
                {{ isEdit ? 'Editar' : 'Nuevo' }} Cliente
              </h1>
              <p class="text-muted mb-0">
                {{ isEdit ? 'Modifica los datos del cliente' : 'Registra un nuevo cliente en el sistema' }}
              </p>
            </div>
            <router-link to="/clientes" class="btn btn-outline-secondary">
              <i class="fas fa-arrow-left me-1"></i>Volver al listado
            </router-link>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-user-edit me-2"></i>
                Información del Cliente
              </h5>
            </div>
            <div class="card-body">
              <form @submit.prevent="handleSubmit" novalidate>
                <!-- Información personal -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-id-card me-2"></i>
                      Datos Personales
                    </h6>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label required">Nombre *</label>
                    <input
                      v-model="form.nombre"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.nombre }"
                      placeholder="Ingresa el nombre"
                      required
                    >
                    <div v-if="errors.nombre" class="invalid-feedback">
                      {{ errors.nombre }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label required">Apellidos *</label>
                    <input
                      v-model="form.apellidos"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.apellidos }"
                      placeholder="Ingresa los apellidos"
                      required
                    >
                    <div v-if="errors.apellidos" class="invalid-feedback">
                      {{ errors.apellidos }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label required">DNI/NIE *</label>
                    <input
                      v-model="form.dni"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.dni }"
                      placeholder="12345678A"
                      pattern="[0-9]{8}[A-Za-z]|[XYZ][0-9]{7}[A-Za-z]"
                      required
                    >
                    <div v-if="errors.dni" class="invalid-feedback">
                      {{ errors.dni }}
                    </div>
                    <div class="form-text">
                      Formato: 12345678A (DNI) o X1234567A (NIE)
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label required">Teléfono *</label>
                    <input
                      v-model="form.telefono"
                      type="tel"
                      class="form-control"
                      :class="{ 'is-invalid': errors.telefono }"
                      placeholder="600123456"
                      pattern="[0-9]{9}"
                      required
                    >
                    <div v-if="errors.telefono" class="invalid-feedback">
                      {{ errors.telefono }}
                    </div>
                    <div class="form-text">
                      Formato: 9 dígitos sin espacios
                    </div>
                  </div>
                </div>

                <div class="row mb-4">
                  <div class="col-md-6">
                    <label class="form-label required">Email *</label>
                    <input
                      v-model="form.email"
                      type="email"
                      class="form-control"
                      :class="{ 'is-invalid': errors.email }"
                      placeholder="<EMAIL>"
                      required
                    >
                    <div v-if="errors.email" class="invalid-feedback">
                      {{ errors.email }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label required">Estado *</label>
                    <select
                      v-model="form.estado"
                      class="form-select"
                      :class="{ 'is-invalid': errors.estado }"
                      required
                    >
                      <option value="">Selecciona un estado</option>
                      <option value="posible">Posible Cliente</option>
                      <option value="confirmado">Cliente Confirmado</option>
                      <option value="activo">Cliente Activo</option>
                      <option value="inactivo">Cliente Inactivo</option>
                    </select>
                    <div v-if="errors.estado" class="invalid-feedback">
                      {{ errors.estado }}
                    </div>
                  </div>
                </div>

                <!-- Información adicional -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-map-marker-alt me-2"></i>
                      Información Adicional
                    </h6>
                  </div>
                </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Dirección</label>
                    <textarea
                      v-model="form.direccion"
                      class="form-control"
                      :class="{ 'is-invalid': errors.direccion }"
                      rows="2"
                      placeholder="Dirección completa del cliente"
                    ></textarea>
                    <div v-if="errors.direccion" class="invalid-feedback">
                      {{ errors.direccion }}
                    </div>
                  </div>
                </div>

                <div class="row mb-4">
                  <div class="col-12">
                    <label class="form-label">Notas</label>
                    <textarea
                      v-model="form.notas"
                      class="form-control"
                      :class="{ 'is-invalid': errors.notas }"
                      rows="3"
                      placeholder="Información adicional sobre el cliente..."
                    ></textarea>
                    <div v-if="errors.notas" class="invalid-feedback">
                      {{ errors.notas }}
                    </div>
                  </div>
                </div>

                <!-- Botones de acción -->
                <div class="row">
                  <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                      <div class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        Los campos marcados con * son obligatorios
                      </div>
                      <div>
                        <router-link to="/clientes" class="btn btn-outline-secondary me-2">
                          <i class="fas fa-times me-1"></i>Cancelar
                        </router-link>
                        <button
                          type="submit"
                          class="btn btn-primary"
                          :disabled="isSubmitting || !isFormValid"
                        >
                          <i class="fas fa-save me-1"></i>
                          {{ isSubmitting ? 'Guardando...' : (isEdit ? 'Actualizar Cliente' : 'Crear Cliente') }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { clientesService } from '@/services/clientes'

export default {
  name: 'ClienteForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()

    const isSubmitting = ref(false)
    const errors = ref({})
    const form = ref({
      nombre: '',
      apellidos: '',
      dni: '',
      telefono: '',
      email: '',
      direccion: '',
      estado: '',
      notas: ''
    })

    const isEdit = computed(() => !!props.id)

    // Validaciones
    const validateDNI = (dni) => {
      const dniRegex = /^[0-9]{8}[A-Za-z]$/
      const nieRegex = /^[XYZ][0-9]{7}[A-Za-z]$/
      return dniRegex.test(dni) || nieRegex.test(dni)
    }

    const validateEmail = (email) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email)
    }

    const validatePhone = (phone) => {
      const phoneRegex = /^[0-9]{9}$/
      return phoneRegex.test(phone)
    }

    const validateForm = () => {
      const newErrors = {}

      // Nombre
      if (!form.value.nombre.trim()) {
        newErrors.nombre = 'El nombre es obligatorio'
      } else if (form.value.nombre.trim().length < 2) {
        newErrors.nombre = 'El nombre debe tener al menos 2 caracteres'
      }

      // Apellidos
      if (!form.value.apellidos.trim()) {
        newErrors.apellidos = 'Los apellidos son obligatorios'
      } else if (form.value.apellidos.trim().length < 2) {
        newErrors.apellidos = 'Los apellidos deben tener al menos 2 caracteres'
      }

      // DNI
      if (!form.value.dni.trim()) {
        newErrors.dni = 'El DNI/NIE es obligatorio'
      } else if (!validateDNI(form.value.dni.trim())) {
        newErrors.dni = 'Formato de DNI/NIE inválido'
      }

      // Teléfono
      if (!form.value.telefono.trim()) {
        newErrors.telefono = 'El teléfono es obligatorio'
      } else if (!validatePhone(form.value.telefono.trim())) {
        newErrors.telefono = 'El teléfono debe tener 9 dígitos'
      }

      // Email
      if (!form.value.email.trim()) {
        newErrors.email = 'El email es obligatorio'
      } else if (!validateEmail(form.value.email.trim())) {
        newErrors.email = 'Formato de email inválido'
      }

      // Estado
      if (!form.value.estado) {
        newErrors.estado = 'El estado es obligatorio'
      }

      errors.value = newErrors
      return Object.keys(newErrors).length === 0
    }

    const isFormValid = computed(() => {
      return form.value.nombre.trim() &&
             form.value.apellidos.trim() &&
             form.value.dni.trim() &&
             form.value.telefono.trim() &&
             form.value.email.trim() &&
             form.value.estado &&
             Object.keys(errors.value).length === 0
    })

    // Limpiar errores cuando el usuario empiece a escribir
    watch(() => form.value.nombre, () => {
      if (errors.value.nombre) delete errors.value.nombre
    })

    watch(() => form.value.apellidos, () => {
      if (errors.value.apellidos) delete errors.value.apellidos
    })

    watch(() => form.value.dni, () => {
      if (errors.value.dni) delete errors.value.dni
    })

    watch(() => form.value.telefono, () => {
      if (errors.value.telefono) delete errors.value.telefono
    })

    watch(() => form.value.email, () => {
      if (errors.value.email) delete errors.value.email
    })

    watch(() => form.value.estado, () => {
      if (errors.value.estado) delete errors.value.estado
    })

    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await clientesService.getById(props.id)
          form.value = { ...form.value, ...response.data }
        } catch (error) {
          console.error('Error al cargar cliente:', error)
          toast.error('Error al cargar los datos del cliente')
          router.push('/clientes')
        }
      }
    })

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isSubmitting.value = true

      try {
        const clienteData = {
          ...form.value,
          nombre: form.value.nombre.trim(),
          apellidos: form.value.apellidos.trim(),
          dni: form.value.dni.trim().toUpperCase(),
          telefono: form.value.telefono.trim(),
          email: form.value.email.trim().toLowerCase(),
          direccion: form.value.direccion.trim(),
          notas: form.value.notas.trim()
        }

        if (isEdit.value) {
          await clientesService.update(props.id, clienteData)
          toast.success('Cliente actualizado correctamente')
        } else {
          await clientesService.create(clienteData)
          toast.success('Cliente creado correctamente')
        }
        router.push('/clientes')
      } catch (error) {
        console.error('Error al guardar cliente:', error)

        if (error.response?.data?.errors) {
          // Errores de validación del backend
          errors.value = error.response.data.errors
          toast.error('Por favor, corrige los errores indicados')
        } else {
          const message = error.response?.data?.message || 'Error al guardar el cliente'
          toast.error(message)
        }
      } finally {
        isSubmitting.value = false
      }
    }

    return {
      form,
      errors,
      isEdit,
      isSubmitting,
      isFormValid,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.required::after {
  content: " *";
  color: #dc3545;
}

.form-control:focus,
.form-select:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
  border-color: #e74a3b;
  box-shadow: 0 0 0 0.2rem rgba(231, 74, 59, 0.25);
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.text-primary {
  color: #4e73df !important;
}

.btn-primary {
  background-color: #4e73df;
  border-color: #4e73df;
}

.btn-primary:hover {
  background-color: #2e59d9;
  border-color: #2653d4;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
}

.invalid-feedback {
  font-size: 0.875rem;
}

.text-muted {
  color: #858796 !important;
}

h6.text-primary {
  font-weight: 600;
  border-bottom: 2px solid #e3e6f0;
  padding-bottom: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #5a5c69;
  margin-bottom: 0.5rem;
}

.card {
  border: 1px solid #e3e6f0;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

@media (max-width: 768px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.justify-content-between > div:last-child {
    display: flex;
    gap: 0.5rem;
  }

  .btn {
    flex: 1;
  }
}
</style>