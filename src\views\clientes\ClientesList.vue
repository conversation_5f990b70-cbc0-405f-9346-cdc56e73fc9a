<template>
  <div class="clientes-list">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Gestión de Clientes</h1>
              <p class="text-muted mb-0">Administra todos los clientes del sistema</p>
            </div>
            <router-link to="/clientes/nuevo" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>Nuevo Cliente
            </router-link>
          </div>
        </div>
      </div>

      <!-- Filtros y búsqueda -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      v-model="searchTerm"
                      type="text"
                      class="form-control"
                      placeholder="Buscar por nombre, DNI, email..."
                    >
                  </div>
                </div>
                <div class="col-md-2">
                  <select v-model="filterEstado" class="form-select">
                    <option value="">Todos los estados</option>
                    <option value="posible">Posible</option>
                    <option value="confirmado">Confirmado</option>
                    <option value="activo">Activo</option>
                    <option value="inactivo">Inactivo</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="sortBy" class="form-select">
                    <option value="nombre">Ordenar por nombre</option>
                    <option value="apellidos">Ordenar por apellidos</option>
                    <option value="email">Ordenar por email</option>
                    <option value="created_at">Ordenar por fecha</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="sortOrder" class="form-select">
                    <option value="asc">Ascendente</option>
                    <option value="desc">Descendente</option>
                  </select>
                </div>
                <div class="col-md-1">
                  <button
                    class="btn btn-outline-secondary w-100"
                    @click="clearFilters"
                    title="Limpiar filtros"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <div class="col-md-1">
                  <button
                    class="btn btn-outline-primary w-100"
                    @click="toggleView"
                    :title="viewMode === 'table' ? 'Vista de tarjetas' : 'Vista de tabla'"
                  >
                    <i :class="viewMode === 'table' ? 'fas fa-th' : 'fas fa-list'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Estadísticas rápidas -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ totalClientes }}</h4>
                  <p class="mb-0">Total Clientes</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-users fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ clientesActivos }}</h4>
                  <p class="mb-0">Clientes Activos</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-user-check fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ clientesPosibles }}</h4>
                  <p class="mb-0">Clientes Posibles</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-user-clock fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ clientesConfirmados }}</h4>
                  <p class="mb-0">Clientes Confirmados</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-user-shield fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contenido principal -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>
                Lista de Clientes ({{ filteredClientes.length }})
              </h5>
              <div class="d-flex align-items-center">
                <span class="text-muted me-3">
                  Mostrando {{ startIndex + 1 }}-{{ endIndex }} de {{ filteredClientes.length }}
                </span>
              </div>
            </div>
            <div class="card-body p-0">
              <!-- Loading -->
              <div v-if="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-2 text-muted">Cargando clientes...</p>
              </div>

              <!-- Sin resultados -->
              <div v-else-if="filteredClientes.length === 0" class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>No se encontraron clientes</h5>
                <p class="text-muted">
                  {{ searchTerm || filterEstado ? 'Intenta ajustar los filtros de búsqueda' : 'Comienza agregando tu primer cliente' }}
                </p>
                <router-link v-if="!searchTerm && !filterEstado" to="/clientes/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Crear Primer Cliente
                </router-link>
              </div>

              <!-- Vista de tabla -->
              <div v-else-if="viewMode === 'table'" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Cliente</th>
                      <th>Contacto</th>
                      <th>Estado</th>
                      <th>Alquileres</th>
                      <th>Fecha Registro</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="cliente in paginatedClientes" :key="cliente.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar me-3">
                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                              {{ getInitials(cliente.nombre, cliente.apellidos) }}
                            </div>
                          </div>
                          <div>
                            <div class="fw-bold">{{ cliente.nombre }} {{ cliente.apellidos }}</div>
                            <div class="text-muted small">{{ cliente.dni }}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div>
                          <div class="small">
                            <i class="fas fa-envelope me-1"></i>{{ cliente.email }}
                          </div>
                          <div class="small text-muted">
                            <i class="fas fa-phone me-1"></i>{{ cliente.telefono }}
                          </div>
                        </div>
                      </td>
                      <td>
                        <span :class="getEstadoClass(cliente.estado)" class="badge">
                          {{ getEstadoText(cliente.estado) }}
                        </span>
                      </td>
                      <td>
                        <span class="badge bg-secondary">
                          {{ cliente.alquileres_count || 0 }} alquiler(es)
                        </span>
                      </td>
                      <td>
                        <div class="small">{{ formatFecha(cliente.created_at) }}</div>
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <router-link
                            :to="`/clientes/${cliente.id}`"
                            class="btn btn-sm btn-outline-primary"
                            title="Ver detalle"
                          >
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link
                            :to="`/clientes/${cliente.id}/editar`"
                            class="btn btn-sm btn-outline-warning"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            @click="confirmarEliminacion(cliente)"
                            class="btn btn-sm btn-outline-danger"
                            title="Eliminar"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Vista de tarjetas -->
              <div v-else-if="viewMode === 'grid'" class="p-3">
                <div class="row">
                  <div v-for="cliente in paginatedClientes" :key="cliente.id" class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100 cliente-card">
                      <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                          <div class="avatar me-3">
                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                              {{ getInitials(cliente.nombre, cliente.apellidos) }}
                            </div>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-0">{{ cliente.nombre }} {{ cliente.apellidos }}</h6>
                            <small class="text-muted">{{ cliente.dni }}</small>
                          </div>
                          <span :class="getEstadoClass(cliente.estado)" class="badge">
                            {{ getEstadoText(cliente.estado) }}
                          </span>
                        </div>

                        <div class="mb-2">
                          <small class="text-muted">
                            <i class="fas fa-envelope me-1"></i>{{ cliente.email }}
                          </small>
                        </div>
                        <div class="mb-2">
                          <small class="text-muted">
                            <i class="fas fa-phone me-1"></i>{{ cliente.telefono }}
                          </small>
                        </div>
                        <div class="mb-3">
                          <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>{{ formatFecha(cliente.created_at) }}
                          </small>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                          <span class="badge bg-secondary">
                            {{ cliente.alquileres_count || 0 }} alquiler(es)
                          </span>
                          <div class="btn-group" role="group">
                            <router-link
                              :to="`/clientes/${cliente.id}`"
                              class="btn btn-sm btn-outline-primary"
                              title="Ver detalle"
                            >
                              <i class="fas fa-eye"></i>
                            </router-link>
                            <router-link
                              :to="`/clientes/${cliente.id}/editar`"
                              class="btn btn-sm btn-outline-warning"
                              title="Editar"
                            >
                              <i class="fas fa-edit"></i>
                            </router-link>
                            <button
                              @click="confirmarEliminacion(cliente)"
                              class="btn btn-sm btn-outline-danger"
                              title="Eliminar"
                            >
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Paginación -->
            <div v-if="totalPages > 1" class="card-footer">
              <nav aria-label="Paginación de clientes">
                <ul class="pagination justify-content-center mb-0">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="currentPage = 1" :disabled="currentPage === 1">
                      <i class="fas fa-angle-double-left"></i>
                    </button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="currentPage--" :disabled="currentPage === 1">
                      <i class="fas fa-angle-left"></i>
                    </button>
                  </li>

                  <li
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === currentPage }"
                  >
                    <button class="page-link" @click="currentPage = page">
                      {{ page }}
                    </button>
                  </li>

                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="currentPage++" :disabled="currentPage === totalPages">
                      <i class="fas fa-angle-right"></i>
                    </button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="currentPage = totalPages" :disabled="currentPage === totalPages">
                      <i class="fas fa-angle-double-right"></i>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmación de eliminación -->
    <div class="modal fade" id="deleteModal" tabindex="-1" ref="deleteModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmar Eliminación</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>¿Estás seguro de que deseas eliminar al cliente <strong>{{ clienteAEliminar?.nombre }} {{ clienteAEliminar?.apellidos }}</strong>?</p>
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Esta acción no se puede deshacer. Se eliminarán todos los datos asociados al cliente.
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button type="button" class="btn btn-danger" @click="eliminarCliente" :disabled="isDeleting">
              <i class="fas fa-trash me-1"></i>
              {{ isDeleting ? 'Eliminando...' : 'Eliminar Cliente' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useToast } from 'vue-toastification'
import { clientesService } from '@/services/clientes'
import { Modal } from 'bootstrap'

export default {
  name: 'ClientesList',
  setup() {
    const toast = useToast()

    // Estado reactivo
    const clientes = ref([])
    const isLoading = ref(false)
    const isDeleting = ref(false)
    const searchTerm = ref('')
    const filterEstado = ref('')
    const sortBy = ref('nombre')
    const sortOrder = ref('asc')
    const viewMode = ref('table')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const clienteAEliminar = ref(null)
    const deleteModal = ref(null)

    // Computed properties
    const filteredClientes = computed(() => {
      let filtered = [...clientes.value]

      // Filtro por búsqueda
      if (searchTerm.value) {
        const term = searchTerm.value.toLowerCase()
        filtered = filtered.filter(cliente =>
          cliente.nombre.toLowerCase().includes(term) ||
          cliente.apellidos.toLowerCase().includes(term) ||
          cliente.dni.toLowerCase().includes(term) ||
          cliente.email.toLowerCase().includes(term) ||
          cliente.telefono.toLowerCase().includes(term)
        )
      }

      // Filtro por estado
      if (filterEstado.value) {
        filtered = filtered.filter(cliente => cliente.estado === filterEstado.value)
      }

      // Ordenamiento
      filtered.sort((a, b) => {
        let aValue = a[sortBy.value] || ''
        let bValue = b[sortBy.value] || ''

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase()
          bValue = bValue.toLowerCase()
        }

        if (sortOrder.value === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })

      return filtered
    })

    const totalClientes = computed(() => clientes.value.length)
    const clientesActivos = computed(() => clientes.value.filter(c => c.estado === 'activo').length)
    const clientesPosibles = computed(() => clientes.value.filter(c => c.estado === 'posible').length)
    const clientesConfirmados = computed(() => clientes.value.filter(c => c.estado === 'confirmado').length)

    const totalPages = computed(() => Math.ceil(filteredClientes.value.length / itemsPerPage.value))
    const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
    const endIndex = computed(() => Math.min(startIndex.value + itemsPerPage.value, filteredClientes.value.length))

    const paginatedClientes = computed(() => {
      return filteredClientes.value.slice(startIndex.value, startIndex.value + itemsPerPage.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    // Métodos
    const loadClientes = async () => {
      isLoading.value = true
      try {
        const response = await clientesService.getAll()
        clientes.value = Array.isArray(response.data.data) ? response.data.data :
                        Array.isArray(response.data) ? response.data : []
      } catch (error) {
        console.error('Error al cargar clientes:', error)
        toast.error('Error al cargar la lista de clientes')
        clientes.value = []
      } finally {
        isLoading.value = false
      }
    }

    const clearFilters = () => {
      searchTerm.value = ''
      filterEstado.value = ''
      currentPage.value = 1
    }

    const toggleView = () => {
      viewMode.value = viewMode.value === 'table' ? 'grid' : 'table'
    }

    const confirmarEliminacion = (cliente) => {
      clienteAEliminar.value = cliente
      const modal = new Modal(deleteModal.value)
      modal.show()
    }

    const eliminarCliente = async () => {
      if (!clienteAEliminar.value) return

      isDeleting.value = true
      try {
        await clientesService.delete(clienteAEliminar.value.id)
        toast.success('Cliente eliminado correctamente')
        await loadClientes()

        const modal = Modal.getInstance(deleteModal.value)
        modal.hide()
        clienteAEliminar.value = null
      } catch (error) {
        console.error('Error al eliminar cliente:', error)
        toast.error('Error al eliminar el cliente')
      } finally {
        isDeleting.value = false
      }
    }

    const getInitials = (nombre, apellidos) => {
      if (!nombre) return 'U'
      const n = nombre.charAt(0).toUpperCase()
      const a = apellidos ? apellidos.charAt(0).toUpperCase() : ''
      return n + a
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'posible': 'bg-warning',
        'confirmado': 'bg-info',
        'activo': 'bg-success',
        'inactivo': 'bg-secondary'
      }
      return classes[estado] || 'bg-secondary'
    }

    const getEstadoText = (estado) => {
      const texts = {
        'posible': 'Posible',
        'confirmado': 'Confirmado',
        'activo': 'Activo',
        'inactivo': 'Inactivo'
      }
      return texts[estado] || 'Desconocido'
    }

    const formatFecha = (fecha) => {
      if (!fecha) return ''
      return new Date(fecha).toLocaleDateString('es-ES')
    }

    // Watchers
    watch([searchTerm, filterEstado], () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      loadClientes()
    })

    return {
      clientes,
      isLoading,
      isDeleting,
      searchTerm,
      filterEstado,
      sortBy,
      sortOrder,
      viewMode,
      currentPage,
      itemsPerPage,
      clienteAEliminar,
      deleteModal,
      filteredClientes,
      totalClientes,
      clientesActivos,
      clientesPosibles,
      clientesConfirmados,
      totalPages,
      startIndex,
      endIndex,
      paginatedClientes,
      visiblePages,
      loadClientes,
      clearFilters,
      toggleView,
      confirmarEliminacion,
      eliminarCliente,
      getInitials,
      getEstadoClass,
      getEstadoText,
      formatFecha
    }
  }
}
</script>

<style scoped>
.cliente-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e3e6f0;
}

.cliente-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.pagination .page-link {
  color: #5a5c69;
  border-color: #e3e6f0;
}

.pagination .page-item.active .page-link {
  background-color: #4e73df;
  border-color: #4e73df;
}

.pagination .page-link:hover {
  color: #4e73df;
  background-color: #f8f9fc;
  border-color: #e3e6f0;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.text-muted {
  color: #858796 !important;
}

.btn-outline-primary:hover {
  background-color: #4e73df;
  border-color: #4e73df;
}

.btn-outline-warning:hover {
  background-color: #f6c23e;
  border-color: #f6c23e;
}

.btn-outline-danger:hover {
  background-color: #e74a3b;
  border-color: #e74a3b;
}

.modal-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

@media (max-width: 768px) {
  .btn-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  .btn-group .btn:last-child {
    margin-bottom: 0;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}
</style>
