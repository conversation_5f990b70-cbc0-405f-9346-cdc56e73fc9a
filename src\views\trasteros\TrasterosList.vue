<template>
  <div class="trasteros-list">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Gestión de Trasteros</h1>
              <p class="text-muted mb-0">Administra todos los trasteros del sistema</p>
            </div>
            <router-link to="/trasteros/nuevo" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>Nuevo Trastero
            </router-link>
          </div>
        </div>
      </div>

      <!-- Filtros y búsqueda -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      v-model="searchTerm"
                      type="text"
                      class="form-control"
                      placeholder="Buscar trasteros..."
                    >
                  </div>
                </div>
                <div class="col-md-2">
                  <select v-model="filterEstado" class="form-select">
                    <option value="">Todos los estados</option>
                    <option value="disponible">Disponible</option>
                    <option value="ocupado">Ocupado</option>
                    <option value="mantenimiento">Mantenimiento</option>
                    <option value="reservado">Reservado</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="filterPlanta" class="form-select">
                    <option value="">Todas las plantas</option>
                    <option v-for="planta in plantas" :key="planta" :value="planta">
                      Planta {{ planta }}
                    </option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="filterTamano" class="form-select">
                    <option value="">Todos los tamaños</option>
                    <option value="pequeño">Pequeño</option>
                    <option value="mediano">Mediano</option>
                    <option value="grande">Grande</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="sortBy" class="form-select">
                    <option value="numero">Ordenar por número</option>
                    <option value="planta">Ordenar por planta</option>
                    <option value="precio">Ordenar por precio</option>
                    <option value="estado">Ordenar por estado</option>
                  </select>
                </div>
                <div class="col-md-1">
                  <button
                    class="btn btn-outline-secondary w-100"
                    @click="clearFilters"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Estadísticas rápidas -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ totalTrasteros }}</h4>
                  <p class="mb-0">Total Trasteros</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-warehouse fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ trasterosDisponibles }}</h4>
                  <p class="mb-0">Disponibles</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-check-circle fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-danger text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ trasterosOcupados }}</h4>
                  <p class="mb-0">Ocupados</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-lock fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ formatPrice(ingresosMensuales) }}</h4>
                  <p class="mb-0">Ingresos/Mes</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-euro-sign fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Lista de trasteros -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="fas fa-warehouse me-2"></i>
                Trasteros ({{ filteredTrasteros.length }})
              </h5>
              <div class="btn-group btn-group-sm">
                <button
                  class="btn btn-outline-primary"
                  @click="loadTrasteros"
                  :disabled="isLoading"
                >
                  <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': isLoading }"></i>
                  Actualizar
                </button>
                <button
                  class="btn btn-outline-secondary"
                  @click="toggleView"
                >
                  <i :class="viewMode === 'table' ? 'fas fa-th' : 'fas fa-list'"></i>
                  {{ viewMode === 'table' ? 'Vista Grid' : 'Vista Tabla' }}
                </button>
                <button class="btn btn-outline-success">
                  <i class="fas fa-download me-1"></i>Exportar
                </button>
              </div>
            </div>
            <div class="card-body p-0">
              <!-- Loading state -->
              <div v-if="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-3 text-muted">Cargando trasteros...</p>
              </div>

              <!-- Empty state -->
              <div v-else-if="filteredTrasteros.length === 0" class="text-center py-5">
                <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay trasteros</h5>
                <p class="text-muted">
                  {{ hasFilters ? 'No se encontraron trasteros con los filtros aplicados' : 'Aún no hay trasteros registrados' }}
                </p>
                <router-link v-if="!hasFilters" to="/trasteros/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Crear primer trastero
                </router-link>
              </div>

              <!-- Vista tabla -->
              <div v-else-if="viewMode === 'table'" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Número</th>
                      <th>Planta</th>
                      <th>Tamaño</th>
                      <th>Precio</th>
                      <th>Estado</th>
                      <th>Cliente</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="trastero in paginatedTrasteros" :key="trastero.id">
                      <td>
                        <div class="fw-bold">{{ trastero.numero }}</div>
                        <small class="text-muted">ID: {{ trastero.id }}</small>
                      </td>
                      <td>{{ trastero.numero_planta }}</td>
                      <td>
                        <span :class="getTamanoClass(trastero.metros_cuadrados)">
                          {{ trastero.metros_cuadrados }}
                        </span>
                      </td>
                      <td class="fw-bold">{{ formatPrice(trastero.precio) }}</td>
                      <td>
                        <span :class="getEstadoClass(trastero.ocupado)">
                          {{ getEstadoText(trastero.ocupado) }}
                        </span>
                      </td>
                      <td>
                        <div v-if="trastero.cliente">
                          <div class="fw-bold">{{ trastero.cliente.nombre }}</div>
                          <small class="text-muted">{{ trastero.cliente.telefono }}</small>
                        </div>
                        <small v-else class="text-muted">Sin asignar</small>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <router-link
                            :to="`/trasteros/${trastero.id}`"
                            class="btn btn-outline-info"
                            title="Ver detalles"
                          >
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link
                            :to="`/trasteros/${trastero.id}/editar`"
                            class="btn btn-outline-primary"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            class="btn btn-outline-danger"
                            @click="confirmDelete(trastero)"
                            title="Eliminar"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Vista grid -->
              <div v-else class="p-3">
                <div class="row">
                  <div v-for="trastero in paginatedTrasteros" :key="trastero.id" class="col-md-4 col-lg-3 mb-3">
                    <div class="card trastero-card" :class="{ 'border-success': trastero.estado === 'disponible', 'border-danger': trastero.estado === 'ocupado' }">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Trastero {{ trastero.numero }}</h6>
                        <span :class="getEstadoClass(trastero.estado)">
                          {{ getEstadoText(trastero.estado) }}
                        </span>
                      </div>
                      <div class="card-body">
                        <div class="mb-2">
                          <small class="text-muted">Planta:</small>
                          <div class="fw-bold">{{ trastero.planta }}</div>
                        </div>
                        <div class="mb-2">
                          <small class="text-muted">Tamaño:</small>
                          <div>{{ trastero.tamano }}</div>
                        </div>
                        <div class="mb-2">
                          <small class="text-muted">Precio:</small>
                          <div class="fw-bold text-primary">{{ formatPrice(trastero.precio) }}/mes</div>
                        </div>
                        <div v-if="trastero.cliente" class="mb-2">
                          <small class="text-muted">Cliente:</small>
                          <div class="fw-bold">{{ trastero.cliente.nombre }}</div>
                        </div>
                      </div>
                      <div class="card-footer">
                        <div class="btn-group w-100">
                          <router-link
                            :to="`/trasteros/${trastero.id}`"
                            class="btn btn-sm btn-outline-info"
                          >
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link
                            :to="`/trasteros/${trastero.id}/editar`"
                            class="btn btn-sm btn-outline-primary"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            class="btn btn-sm btn-outline-danger"
                            @click="confirmDelete(trastero)"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Paginación -->
              <div v-if="totalPages > 1" class="card-footer">
                <nav aria-label="Paginación de trasteros">
                  <ul class="pagination pagination-sm justify-content-center mb-0">
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="goToPage(currentPage - 1)">
                        <i class="fas fa-chevron-left"></i>
                      </button>
                    </li>

                    <li
                      v-for="page in visiblePages"
                      :key="page"
                      class="page-item"
                      :class="{ active: page === currentPage }"
                    >
                      <button class="page-link" @click="goToPage(page)">
                        {{ page }}
                      </button>
                    </li>

                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="goToPage(currentPage + 1)">
                        <i class="fas fa-chevron-right"></i>
                      </button>
                    </li>
                  </ul>
                </nav>

                <div class="text-center mt-2">
                  <small class="text-muted">
                    Mostrando {{ startIndex + 1 }} - {{ Math.min(endIndex, filteredTrasteros.length) }}
                    de {{ filteredTrasteros.length }} trasteros
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmación -->
    <div v-if="showDeleteModal" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmar eliminación</h5>
            <button
              type="button"
              class="btn-close"
              @click="showDeleteModal = false"
            ></button>
          </div>
          <div class="modal-body">
            <p>¿Estás seguro de que quieres eliminar el trastero <strong>{{ trasteroToDelete?.numero }}</strong>?</p>
            <p class="text-danger">
              <i class="fas fa-exclamation-triangle me-1"></i>
              Esta acción no se puede deshacer.
            </p>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              @click="showDeleteModal = false"
            >
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-danger"
              @click="deleteTrastero"
              :disabled="isDeleting"
            >
              <i class="fas fa-trash me-1"></i>
              {{ isDeleting ? 'Eliminando...' : 'Eliminar' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { trasterosService } from '@/services/trasteros'

export default {
  name: 'TrasterosList',
  setup() {
    const toast = useToast()

    const trasteros = ref([])
    const isLoading = ref(false)
    const isDeleting = ref(false)
    const searchTerm = ref('')
    const filterEstado = ref('')
    const filterPlanta = ref('')
    const filterTamano = ref('')
    const sortBy = ref('numero')
    const currentPage = ref(1)
    const itemsPerPage = 12
    const viewMode = ref('table') // 'table' o 'grid'
    const showDeleteModal = ref(false)
    const trasteroToDelete = ref(null)

    const filteredTrasteros = computed(() => {
      let filtered = trasteros.value

      if (searchTerm.value) {
        const term = searchTerm.value.toLowerCase()
        filtered = filtered.filter(trastero =>
          trastero.numero.toLowerCase().includes(term) ||
          trastero.planta.toLowerCase().includes(term) ||
          trastero.tamano.toLowerCase().includes(term) ||
          (trastero.cliente && trastero.cliente.nombre.toLowerCase().includes(term))
        )
      }

      if (filterEstado.value) {
        filtered = filtered.filter(trastero => trastero.estado === filterEstado.value)
      }

      if (filterPlanta.value) {
        filtered = filtered.filter(trastero => trastero.planta === filterPlanta.value)
      }

      if (filterTamano.value) {
        filtered = filtered.filter(trastero => trastero.tamano === filterTamano.value)
      }

      // Ordenar
      filtered.sort((a, b) => {
        switch (sortBy.value) {
          case 'numero':
            return a.numero.localeCompare(b.numero)
          case 'planta':
            return a.planta.localeCompare(b.planta)
          case 'precio':
            return a.precio - b.precio
          case 'estado':
            return a.estado.localeCompare(b.estado)
          default:
            return 0
        }
      })

      return filtered
    })

    const plantas = computed(() => {
      // Check if trasteros.value is an array before using map
      if (!Array.isArray(trasteros.value)) {
        return []
      }
      const plantasSet = new Set(trasteros.value.map(t => t.planta))
      return Array.from(plantasSet).sort()
    })

    const totalTrasteros = computed(() => Array.isArray(trasteros.value) ? trasteros.value.length : 0)
    const trasterosDisponibles = computed(() => Array.isArray(trasteros.value) ? trasteros.value.filter(t => t.estado === 'disponible').length : 0)
    const trasterosOcupados = computed(() => Array.isArray(trasteros.value) ? trasteros.value.filter(t => t.estado === 'ocupado').length: 0)
    const ingresosMensuales = computed(() => {
      return trasteros.value
        .filter(t => t.estado === 'ocupado')
        .reduce((total, t) => total + t.precio, 0)
    })

    const hasFilters = computed(() => {
      return searchTerm.value || filterEstado.value || filterPlanta.value || filterTamano.value
    })

    const totalPages = computed(() => Math.ceil(filteredTrasteros.value.length / itemsPerPage))
    const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)
    const endIndex = computed(() => startIndex.value + itemsPerPage)

    const paginatedTrasteros = computed(() => {
      return filteredTrasteros.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    })

    const formatPrice = (price) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(price)
    }

    const getTamanoClass = (tamano) => {
      const classes = {
        'pequeño': 'badge bg-info',
        'mediano': 'badge bg-warning text-dark',
        'grande': 'badge bg-success'
      }
      return classes[tamano] || 'badge bg-secondary'
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'disponible': 'badge bg-success',
        'ocupado': 'badge bg-danger',
        'mantenimiento': 'badge bg-warning text-dark',
        'reservado': 'badge bg-info'
      }
      return classes[estado] || 'badge bg-secondary'
    }

    const getEstadoText = (estado) => {
      const texts = {
        'disponible': 'Disponible',
        'ocupado': 'Ocupado',
        'mantenimiento': 'Mantenimiento',
        'reservado': 'Reservado'
      }
      return texts[estado] || estado
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const clearFilters = () => {
      searchTerm.value = ''
      filterEstado.value = ''
      filterPlanta.value = ''
      filterTamano.value = ''
      currentPage.value = 1
    }

    const toggleView = () => {
      viewMode.value = viewMode.value === 'table' ? 'grid' : 'table'
    }

    const loadTrasteros = async () => {
      isLoading.value = true
      try {
        const response = await trasterosService.getAll()
        // Ensure response.data is an array

        console.log('loadTrasteros', response.data);

        trasteros.value = Array.isArray(response.data.data) ? response.data.data : []
      } catch (error) {
        console.error('Error al cargar trasteros:', error)
        toast.error('Error al cargar la lista de trasteros')
        trasteros.value = [] // Limpiar la lista en caso de error
      } finally {
        isLoading.value = false
      }
    }

    const confirmDelete = (trastero) => {
      trasteroToDelete.value = trastero
      showDeleteModal.value = true
    }

    const deleteTrastero = async () => {
      if (!trasteroToDelete.value) return

      isDeleting.value = true
      try {
        await trasterosService.delete(trasteroToDelete.value.id)

        trasteros.value = Array.isArray(trasteros.value) ? trasteros.value.filter(t => t.id !== trasteroToDelete.value.id) : null;

        showDeleteModal.value = false
        trasteroToDelete.value = null
        toast.success('Trastero eliminado correctamente')
      } catch (error) {
        console.error('Error al eliminar trastero:', error)
        toast.error('Error al eliminar el trastero')
      } finally {
        isDeleting.value = false
      }
    }

    onMounted(() => {
      loadTrasteros()
    })

    return {
      trasteros,
      isLoading,
      isDeleting,
      searchTerm,
      filterEstado,
      filterPlanta,
      filterTamano,
      sortBy,
      currentPage,
      itemsPerPage,
      viewMode,
      showDeleteModal,
      trasteroToDelete,
      filteredTrasteros,
      plantas,
      totalTrasteros,
      trasterosDisponibles,
      trasterosOcupados,
      ingresosMensuales,
      hasFilters,
      totalPages,
      startIndex,
      endIndex,
      paginatedTrasteros,
      visiblePages,
      formatPrice,
      getTamanoClass,
      getEstadoClass,
      getEstadoText,
      goToPage,
      clearFilters,
      toggleView,
      loadTrasteros,
      confirmDelete,
      deleteTrastero
    }
  }
}
</script>

<style scoped>
.trasteros-list .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.trasteros-list .card-title {
  color: #495057;
  font-weight: 600;
}

.trastero-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.trastero-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.trasteros-list .table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.trasteros-list .table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.trasteros-list .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.trasteros-list .btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.trasteros-list .spinner-border {
  width: 2rem;
  height: 2rem;
}

.trasteros-list .modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.trasteros-list .pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.trasteros-list .input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

.trasteros-list .card.bg-primary,
.trasteros-list .card.bg-success,
.trasteros-list .card.bg-danger,
.trasteros-list .card.bg-info {
  border: none;
}

.trasteros-list .card.bg-primary .card-body,
.trasteros-list .card.bg-success .card-body,
.trasteros-list .card.bg-danger .card-body,
.trasteros-list .card.bg-info .card-body {
  padding: 1.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .trasteros-list .card-body {
    padding: 0.5rem;
  }

  .trasteros-list .table-responsive {
    font-size: 0.8rem;
  }

  .trasteros-list .btn-group {
    flex-direction: column;
  }

  .trasteros-list .btn-group .btn {
    margin-bottom: 0.25rem;
  }

  .trasteros-list .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .trasteros-list .col-md-3,
  .trasteros-list .col-md-2,
  .trasteros-list .col-md-1 {
    margin-bottom: 0.5rem;
  }

  .trastero-card {
    margin-bottom: 1rem;
  }
}

/* Print styles */
@media print {
  .trasteros-list .btn,
  .trasteros-list .btn-group,
  .trasteros-list .pagination {
    display: none !important;
  }

  .trasteros-list .card {
    border: 1px solid #000;
    break-inside: avoid;
  }

  .trastero-card {
    transform: none !important;
    box-shadow: none !important;
  }
}
</style>



